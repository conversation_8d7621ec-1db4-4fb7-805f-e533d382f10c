import pandas as pd
import numpy as np
import os
import matplotlib.pyplot as plt
import pickle
from datetime import datetime, timedelta
import random
import seaborn as sns
from openpyxl import load_workbook
from openpyxl.drawing.image import Image as XLImage
import warnings
warnings.filterwarnings("ignore", category=FutureWarning)

import torch
import torch.nn as nn
import torch.optim as optim

# 数据读取
class FlightDataLoader:
    def __init__(self, excel_path, mode='train'):
        self.excel_path = excel_path
        self.mode = mode
        self.data = self.load_data()

    def load_data(self):
        df = pd.read_excel(self.excel_path)
        df['scheduled_departure'] = pd.to_datetime(df['scheduled_departure'])
        if 'actual_departure' in df.columns:
            df['actual_departure'] = pd.to_datetime(df['actual_departure'])
        if 'expected_departure' in df.columns:
            df['expected_departure'] = pd.to_datetime(df['expected_departure'])
        if 'passenger_count_max' not in df.columns:
            df['passenger_count_max'] = np.nan # 或其他默认值
        if 'arrival_place' not in df.columns:
            df['arrival_place'] = "" # 或其他默认值
        cols_needed = ['flight_id', 'scheduled_departure', 'actual_departure', 'expected_departure', 'passenger_count', 'passenger_count_max', 'arrival_place']
        cols = [col for col in cols_needed if col in df.columns]
        df = df[cols]
        return df

    def get_data(self):
        return self.data
# 时隙空间和筛选可用时隙
def generate_time_slots(window_start, window_end, slot_length=5):
    slots = []
    t = window_start
    while t < window_end:
        slot_end = t + timedelta(minutes=slot_length)
        slots.append({'slot_start': t, 'slot_end': slot_end})
        t = slot_end
    return slots

def get_slot_index_for_time(slots, dt):
    for idx, slot in enumerate(slots):
        if slot['slot_start'] <= dt < slot['slot_end']:
            return idx
    return None

def filter_available_slots(df, window_start, window_end, slot_length=5):
    slots = generate_time_slots(window_start, window_end, slot_length)
    available = [False] * len(slots)
    for idx, row in df.iterrows():
        sch_dep = row['scheduled_departure']
        slot_idx = get_slot_index_for_time(slots, sch_dep)
        if slot_idx is not None:
            available[slot_idx] = True
    return available

def build_global_slot_index(slots_per_window):
    slot_key2idx = dict()
    idx2slot = dict()
    idx = 0
    for slots in slots_per_window:
        for slot in slots:
            key = (slot['slot_start'], slot['slot_end'])
            if key not in slot_key2idx:
                slot_key2idx[key] = idx
                idx2slot[idx] = slot
                idx += 1
    return slot_key2idx, idx2slot

def delay_cost_caculate(delay, passenger_count):
    # Handle pandas Series input
    return np.where(delay < 240, 0,
                    np.where(delay <= 480, 200 * passenger_count,
                             400 * passenger_count)).astype(int)

class FlightRecoveryEnv:
    def __init__(
        self, flights_df, slots,
        available_slots, slot_length=5, max_per_slot=5,
        global_slot_key2idx=None, global_slot_counts=None,
        slot_reward_pos=1.0, slot_reward_neg=-0.7,
        cancel_penalty=-2000,
        allow_cancel=True, 
        use_expected_depature=False   
    ):
        self.flights_df = flights_df.reset_index(drop=True)
        self.slots = slots
        self.available_slots = available_slots
        self.slot_length = slot_length
        self.max_per_slot = max_per_slot

        self.slot_counts = [0 for _ in self.slots]
        self.flight_assignments = [-1 for _ in range(len(self.flights_df))]
        self.state = (tuple(self.flight_assignments), tuple(self.slot_counts))
        self.done = False

        self.global_slot_key2idx = global_slot_key2idx
        self.global_slot_counts = global_slot_counts

        self.use_expected_depature = use_expected_depature

        self.original_delays = []
        for idx, flight in self.flights_df.iterrows():
            sch_dep = flight['scheduled_departure']
            if self.use_expected_depature and 'expected_departure' in flight:
                actual_dep = flight['expected_departure']
            else:
                actual_dep = flight['actual_departure']
            delay = (actual_dep - sch_dep).total_seconds() / 60.0 - 15
            self.original_delays.append(max(delay, 0))

        self.slot_reward_pos = slot_reward_pos
        self.slot_reward_neg = slot_reward_neg
        self.cancel_penalty = cancel_penalty
        self.allow_cancel = allow_cancel  

    def get_action_mask(self):
        n_flight = len(self.flights_df)
        n_slot = len(self.slots)
        mask = np.zeros((n_flight, n_slot + 1), dtype=np.bool_)  
        for idx, assigned in enumerate(self.flight_assignments):
            if assigned == -1:
                flight = self.flights_df.iloc[idx]
                sch_dep = flight['scheduled_departure']
                if self.use_expected_depature and 'expected_departure' in flight:
                    actual_dep = flight['expected_departure']
                else:
                    actual_dep = flight['actual_departure']
                for slot_idx, slot in enumerate(self.slots):
                    if slot['slot_start'] > actual_dep  + timedelta(minutes=20):
                        continue
                    if not self.available_slots[slot_idx]:
                        continue
                    if self.get_slot_remain_capacity(slot_idx) <= 0:
                        continue
                    if slot['slot_start'] < sch_dep-timedelta(minutes=10):
                        continue
                    mask[idx, slot_idx] = True
                if self.allow_cancel:
                    mask[idx, n_slot] = True
                else:
                    mask[idx, n_slot] = False
        return mask
    def get_slot_remain_capacity(self, slot_idx):
        cap_local = self.slot_counts[slot_idx]
        if (self.global_slot_key2idx is not None) and (self.global_slot_counts is not None):
            key = (self.slots[slot_idx]['slot_start'], self.slots[slot_idx]['slot_end'])
            global_idx = self.global_slot_key2idx.get(key, None)
            cap_global = self.global_slot_counts[global_idx] if global_idx is not None else 0
            return self.max_per_slot - (cap_local + cap_global)
        else:
            return self.max_per_slot - cap_local

    def step(self, action):
        flight_idx, slot_idx = action
        if self.flight_assignments[flight_idx] != -1:
            raise Exception(f"航班{flight_idx}已分配，不可重复分配")
        n_slot = len(self.slots)
        flight = self.flights_df.iloc[flight_idx]
        sch_dep = flight['scheduled_departure']
        if self.use_expected_depature and 'expected_departure' in flight:
            actual_dep = flight['expected_departure']
        else:
            actual_dep = flight['actual_departure']
        passenger_count = flight['passenger_count']
        original_delay = self.original_delays[flight_idx]

        # 取消动作
        if slot_idx == n_slot:
            self.flight_assignments[flight_idx] = -2  # -2表示取消
            reward = self.cancel_penalty
            done = all([x != -1 for x in self.flight_assignments])
            self.done = done
            self.state = (tuple(self.flight_assignments), tuple(self.slot_counts))
            return self.state, reward, done

        prev_slot_count = self.slot_counts[slot_idx]
        self.flight_assignments[flight_idx] = slot_idx
        self.slot_counts[slot_idx] += 1
        new_sch_departure = self.slots[slot_idx]['slot_start']
        if (self.global_slot_key2idx is not None) and (self.global_slot_counts is not None):
            key = (self.slots[slot_idx]['slot_start'], self.slots[slot_idx]['slot_end'])
            global_idx = self.global_slot_key2idx.get(key, None)
            if global_idx is not None:
                self.global_slot_counts[global_idx] += 1

        delay = (actual_dep - new_sch_departure).total_seconds() / 60.0 -15
        delay = max(delay, 0)

        reward_delay = -(delay - original_delay) / 100.0

        if delay < 240:
            delay_cost = 0
        elif delay <= 480 :
            delay_cost = int(200 * passenger_count)
        else:
            delay_cost = int(400 * passenger_count)
        if original_delay < 240:
            original_delay_cost = 0
        elif original_delay <= 480 :
            original_delay_cost = int(200 * passenger_count)
        else:
            original_delay_cost = int(400 * passenger_count)

        reward_cost = -(delay_cost - original_delay_cost) / 2000.0

        slot_use_reward = 0
        if prev_slot_count == 0:
            slot_use_reward = self.slot_reward_pos
        elif prev_slot_count in [1, 2, 3, 4]:
            slot_use_reward = self.slot_reward_neg

        reward = reward_delay + reward_cost + slot_use_reward

        done = all([x != -1 for x in self.flight_assignments])
        self.done = done
        self.state = (tuple(self.flight_assignments), tuple(self.slot_counts))
        return self.state, reward, done

    def reset(self):
        self.slot_counts = [0 for _ in self.slots]
        self.flight_assignments = [-1 for _ in range(len(self.flights_df))]
        self.state = (tuple(self.flight_assignments), tuple(self.slot_counts))
        self.done = False
        return self.state


    def get_assignment_results(self, use_actual_for_stats=False):
        results = []
        n_slot = len(self.slots)
        for idx, slot_idx in enumerate(self.flight_assignments):
            flight = self.flights_df.iloc[idx]
            result = {
                'flight_id': flight['flight_id'],
                'scheduled_departure': flight['scheduled_departure'],
                'actual_departure': flight['actual_departure'] if 'actual_departure' in flight else None,
                'expected_departure': flight['expected_departure'] if 'expected_departure' in flight else None,
                'passenger_count': flight['passenger_count']
            }
            sch_dep = flight['scheduled_departure']
            if use_actual_for_stats and 'actual_departure' in flight and 'expected_departure' in flight:
                actual_dep = flight['actual_departure']
                expected_dep = flight['expected_departure']
            elif 'expected_departure' in flight:
                actual_dep = flight['expected_departure']
                expected_dep = flight['expected_departure']
            else:
                actual_dep = flight['actual_departure']
                expected_dep = flight.get('expected_departure', None) or flight['actual_departure']

            if slot_idx == -1:
                result['new_scheduled_departure'] = sch_dep - timedelta(minutes=5)
                result['new_delay_minutes'] = max(0,(expected_dep - sch_dep).total_seconds() / 60.0 -15)
                result['new_delay_cost'] = result['new_delay_minutes'] * flight['passenger_count']
                result['cancelled'] = False
                result['origin_delay_minutes'] = max(0,(expected_dep - sch_dep).total_seconds() / 60.0 -15)
                result['origin_delay_cost'] = delay_cost_caculate(result['origin_delay_minutes'],flight['passenger_count'])
                # 真实
                result['ture_new_delay'] = max(0,(actual_dep - sch_dep).total_seconds() / 60.0 -15)
                result['ture_new_cost'] = result['ture_new_delay'] * flight['passenger_count']
                result['ture_origin_delay'] = max(0,(actual_dep - sch_dep).total_seconds() / 60.0 -15)
                result['ture_origin_cost'] = delay_cost_caculate(result['ture_origin_delay'] , flight['passenger_count'])
            elif slot_idx == n_slot or slot_idx == -2:
                result['new_scheduled_departure'] = None
                result['new_delay_minutes'] = 9999
                result['new_delay_cost'] = 9999 * flight['passenger_count']
                result['cancelled'] = True
                result['origin_delay_minutes'] = max(0,(expected_dep - sch_dep).total_seconds() / 60.0 -15)
                result['origin_delay_cost'] = result['origin_delay_minutes'] * flight['passenger_count']
                result['ture_new_delay'] = 9999
                result['ture_new_cost'] = 9999 * flight['passenger_count']
                result['ture_origin_delay'] = max(0,(actual_dep - sch_dep).total_seconds() / 60.0 -15)
                result['ture_origin_cost'] = delay_cost_caculate(result['ture_origin_delay'] , flight['passenger_count'])
            else:
                new_sch_dep = self.slots[slot_idx]['slot_start']
                delay = (expected_dep - new_sch_dep).total_seconds() / 60.0 -15
                delay = max(delay, 0)
                result['new_scheduled_departure'] = new_sch_dep
                result['new_delay_minutes'] = delay
                result['new_delay_cost'] = delay_cost_caculate(delay , flight['passenger_count'])
                result['cancelled'] = False
                result['origin_delay_minutes'] = max(0,(expected_dep - sch_dep).total_seconds() / 60.0 -15)
                result['origin_delay_cost'] = delay_cost_caculate(result['origin_delay_minutes'] , flight['passenger_count'])
                # 真实
                delay_true = (actual_dep - new_sch_dep).total_seconds() / 60.0 -15
                delay_true = max(delay_true, 0)
                result['ture_new_delay'] = delay_true
                result['ture_new_cost'] = delay_cost_caculate(delay_true , flight['passenger_count'])
                result['ture_origin_delay'] = max(0,(actual_dep - sch_dep).total_seconds() / 60.0 -15)
                result['ture_origin_cost'] = delay_cost_caculate(result['ture_origin_delay'] , flight['passenger_count'])
            results.append(result)
        return pd.DataFrame(results)

def flatten_state(state):
    return np.array(state[0] + state[1], dtype=np.float32)

class MaskedActorCritic(nn.Module):
    def __init__(self, state_dim, action_dim, hidden_dim=128):
        super(MaskedActorCritic, self).__init__()
        self.fc1 = nn.Linear(state_dim, hidden_dim)
        self.fc2 = nn.Linear(hidden_dim, hidden_dim)
        self.actor_head = nn.Linear(hidden_dim, action_dim)
        self.critic_head = nn.Linear(hidden_dim, 1)

    def forward(self, x):
        x = torch.relu(self.fc1(x))
        x = torch.relu(self.fc2(x))
        logits = self.actor_head(x)
        value = self.critic_head(x)
        return logits, value

    def get_action(self, state, action_mask, device='cpu', temperature=2.0):
        state_tensor = torch.tensor(flatten_state(state), dtype=torch.float32).unsqueeze(0).to(device)
        logits, value = self.forward(state_tensor)
        mask_tensor = torch.tensor(action_mask.flatten(), dtype=torch.bool).to(device)
        logits = logits.masked_fill(~mask_tensor, float('-inf'))
        probs = torch.softmax(logits / temperature, dim=-1)
        dist = torch.distributions.Categorical(probs)
        action_idx = dist.sample().item()
        log_prob = dist.log_prob(torch.tensor(action_idx).to(device)).item()
        return action_idx, log_prob, value.item(), probs.cpu().detach().numpy()

def moving_average(data, window_size=20):
    if len(data) < window_size:
        return np.array(data)
    return np.convolve(data, np.ones(window_size)/window_size, mode='valid')

class TrajectoryBuffer:
    def __init__(self):
        self.reset()

    def reset(self):
        self.states, self.action_idxs, self.actions, self.rewards, self.dones, self.log_probs, self.values, self.masks = [], [], [], [], [], [], [], []

    def store(self, state, action_idx, action, reward, done, log_prob, value, mask):
        self.states.append(flatten_state(state))
        self.action_idxs.append(action_idx)
        self.actions.append(action)
        self.rewards.append(reward)
        self.dones.append(done)
        self.log_probs.append(log_prob)
        self.values.append(value)
        self.masks.append(mask.flatten())

    def get(self):
        return (
            np.array(self.states),
            np.array(self.action_idxs),
            self.actions,
            np.array(self.rewards),
            np.array(self.dones),
            np.array(self.log_probs),
            np.array(self.values),
            np.array(self.masks)
        )

def decode_action(action_idx, n_flight, n_slot):
    slot_plus = n_slot + 1
    flight_idx = action_idx // slot_plus
    slot_idx = action_idx % slot_plus
    return (flight_idx, slot_idx)

def train_masked_ppo(env, num_episodes=2000, gamma=0.99, lam=0.95,
                     lr=1e-4, batch_size=128, ppo_epochs=4, clip_eps=0.2, entropy_coef=0.05,
                     model_save_path='ppo_model.pt'):
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    state_dim = len(flatten_state(env.state))
    n_flight = len(env.flights_df)
    n_slot = len(env.slots)
    action_dim = n_flight * (n_slot + 1)  # 注意+1
    agent = MaskedActorCritic(state_dim, action_dim).to(device)
    optimizer = optim.Adam(agent.parameters(), lr=lr)

    rewards_history = []
    total_delay_history = []
    total_cost_history = []
    best_assignment = None
    best_total_delay = None

    for episode in range(num_episodes):
        buffer = TrajectoryBuffer()
        state = env.reset()
        done = False
        steps = 0
        while not done:
            action_mask = env.get_action_mask()
            mask_flat = action_mask.flatten()
            if not mask_flat.any():
                break
            with torch.no_grad():
                action_idx, log_prob, value, _ = agent.get_action(state, action_mask, device=device, temperature=2.0)
            flight_idx, slot_idx = decode_action(action_idx, n_flight, n_slot)

            if not mask_flat[action_idx]:
                possible_idxs = np.where(mask_flat)[0]
                if len(possible_idxs) > 0:
                    action_idx = np.random.choice(possible_idxs)
                    flight_idx, slot_idx = decode_action(action_idx, n_flight, n_slot)
                else:
                    # If no valid actions are available, break the loop
                    break

            next_state, reward, done = env.step((flight_idx, slot_idx))
            buffer.store(state, action_idx, (flight_idx, slot_idx), reward, done, log_prob, value, action_mask)
            state = next_state
            steps += 1

        states, action_idxs, actions, rewards, dones, log_probs, values, masks = buffer.get()
        advs = []
        returns = []
        lastgaelam = 0
        next_value = 0
        for t in reversed(range(len(rewards))):
            nonterminal = 1.0 - dones[t]
            next_value = values[t+1] if t+1 < len(values) else 0
            delta = rewards[t] + gamma * next_value * nonterminal - values[t]
            lastgaelam = delta + gamma * lam * nonterminal * lastgaelam
            advs.insert(0, lastgaelam)
            returns.insert(0, lastgaelam + values[t])
        advs = np.array(advs, dtype=np.float32)
        returns = np.array(returns, dtype=np.float32)

        states_tensor = torch.tensor(states, dtype=torch.float32).to(device)
        action_idxs_tensor = torch.tensor(action_idxs, dtype=torch.long).to(device)
        returns_tensor = torch.tensor(returns, dtype=torch.float32).to(device)
        advs_tensor = torch.tensor(advs, dtype=np.float32).to(device)
        old_log_probs_tensor = torch.tensor(log_probs, dtype=torch.float32).to(device)
        masks_tensor = torch.tensor(masks, dtype=torch.bool).to(device)

        n_total = len(states)
        for _ in range(ppo_epochs):
            perm = np.random.permutation(n_total)
            for i in range(0, n_total, batch_size):
                idx = perm[i:i+batch_size]
                batch_states = states_tensor[idx]
                batch_action_idxs = action_idxs_tensor[idx]
                batch_returns = returns_tensor[idx]
                batch_advs = advs_tensor[idx]
                batch_old_log_probs = old_log_probs_tensor[idx]
                batch_masks = masks_tensor[idx]

                logits, value = agent(batch_states)
                batch_logits = logits
                batch_logits[~batch_masks] = float('-inf')
                probs = torch.softmax(batch_logits / 2.0, dim=-1)
                dist = torch.distributions.Categorical(probs)
                new_log_probs = dist.log_prob(batch_action_idxs)
                entropy = dist.entropy().mean()

                ratio = torch.exp(new_log_probs - batch_old_log_probs)
                surr1 = ratio * batch_advs
                surr2 = torch.clamp(ratio, 1.0-clip_eps, 1.0+clip_eps) * batch_advs
                policy_loss = -torch.min(surr1, surr2).mean()
                value_loss = nn.functional.mse_loss(value.squeeze(-1), batch_returns)
                loss = policy_loss + 0.5*value_loss - entropy_coef*entropy

                optimizer.zero_grad()
                loss.backward()
                optimizer.step()

        assignment = env.get_assignment_results()
        cancel_count_t = assignment['cancelled'].sum()
        total_delay = assignment['new_delay_minutes'].fillna(0).sum()
        total_delay_history.append(total_delay)
        total_cost = assignment['new_delay_cost'].fillna(0).sum()
        total_cost_history.append(total_cost)
        ep_reward = sum(rewards)
        rewards_history.append(ep_reward)
        assigned_slot_count = sum([cnt > 0 for cnt in env.slot_counts])
        available_slot_count = sum(env.available_slots)
        slot_assign_ratio = assigned_slot_count / available_slot_count if available_slot_count > 0 else 0

        if (best_total_delay is None) or (total_delay < best_total_delay):
            best_total_delay = total_delay
            best_assignment = assignment.copy()

        print(f"Episode {episode+1}/{num_episodes}, Reward: {ep_reward:.2f}, Total Delay: {total_delay:.2f}, Total Cost: {total_cost:.2f}, "
            f"Cancelled: {assignment['cancelled'].sum()}, Assigned Slots: {assigned_slot_count}/{available_slot_count} ({slot_assign_ratio:.2%})")

    torch.save(agent.state_dict(), model_save_path)
    print(f'Model saved to {model_save_path}')

    # Plotting for all episodes 
    plt.figure(figsize=(16, 6))
    plt.subplot(1,3,1)
    plt.plot(rewards_history, label='Reward', alpha=0.5)
    ma_rewards = moving_average(rewards_history, window_size=20)
    plt.plot(range(len(ma_rewards)), ma_rewards, label='Reward Trend', color='red', linewidth=2)
    plt.title('Training Reward (All Episodes)')
    plt.xlabel('Episode')
    plt.ylabel('Reward')
    plt.legend()
    plt.subplot(1,3,2)
    plt.plot(total_delay_history, label='Total Delay', alpha=0.5)
    ma_delays = moving_average(total_delay_history, window_size=20)
    plt.plot(range(len(ma_delays)), ma_delays, label='Delay Trend', color='red', linewidth=2)
    plt.title('Total Delay per Episode')
    plt.xlabel('Episode')
    plt.ylabel('Total Delay (min)')
    plt.legend()
    plt.subplot(1,3,3)
    plt.plot(total_cost_history, label='Total Delay Cost', alpha=0.5)
    ma_costs = moving_average(total_cost_history, window_size=20)
    plt.plot(range(len(ma_costs)), ma_costs, label='Cost Trend', color='red', linewidth=2)
    plt.title('Total Delay Cost per Episode')
    plt.xlabel('Episode')
    plt.ylabel('Total Cost')
    plt.legend()
    plt.tight_layout()
    plt.savefig('training_curves_ppo_all.png')
    plt.show()

    return agent, rewards_history, total_delay_history, total_cost_history, assignment, best_assignment

def visualize_assignment(assignment_df, save_path='assignment_result_last.xlsx', tag='LAST'):
    assignment_df.to_excel(save_path, index=False)
    print(f'Assignment result ({tag}) saved to {save_path}')
    plt.figure(figsize=(10, 5))
    plt.hist(assignment_df[~assignment_df['cancelled']]['new_delay_minutes'].dropna(), bins=20, color='skyblue', edgecolor='k')
    plt.title(f'Final Delay Distribution ({tag})')
    plt.xlabel('Delay (minutes)')
    plt.ylabel('Number of Flights')
    plt.savefig(f'final_delay_hist_{tag.lower()}.png')
    plt.show()
    if 'cancelled' in assignment_df.columns:
        plt.figure(figsize=(6,4))
        plt.bar(['Not Cancelled','Cancelled'], [(~assignment_df['cancelled']).sum(), assignment_df['cancelled'].sum()], color=['skyblue','salmon'])
        plt.title(f'Number of Cancelled Flights ({tag})')
        plt.ylabel('Count')
        plt.savefig(f'cancelled_flights_{tag.lower()}.png')
        plt.show()

def statistics_compare(original_df, assignment_last, assignment_best):
    origin_cancel_count = 0
    origin_count = len(original_df)
    for tag, assignment in [('LAST', assignment_last), ('BEST', assignment_best)]:
        result_cancel_count = assignment['cancelled'].sum()
        result_total_delay = assignment['new_delay_minutes'].fillna(0).sum()
        origin_total_delay = assignment['origin_delay_minutes'].fillna(0).sum()
        result_total_cost = assignment['new_delay_cost'].fillna(0).sum()
        origin_total_cost = assignment['origin_delay_cost'].fillna(0).sum()
        result_count = len(assignment)
        cancel_rate_origin = 0 if origin_count == 0 else origin_cancel_count / result_count
        cancel_rate_result = 0 if result_count == 0 else result_cancel_count / result_count
        delay_rate_origin = 0 if origin_count == 0 else origin_total_delay / result_count
        delay_rate_result = 0 if result_count == 0 else result_total_delay / result_count
        cost_rate_origin = 0 if origin_count == 0 else origin_total_cost / result_count
        cost_rate_result = 0 if result_count == 0 else result_total_cost / result_count

        print(f'--- {tag} 排班结果统计 ---')
        print(f'原始取消率: {cancel_rate_origin:.4f}, 新取消率: {cancel_rate_result:.4f}, 变化: {cancel_rate_result-cancel_rate_origin:.4f}')
        print(f'原始平均延误: {delay_rate_origin:.2f}, 新平均延误: {delay_rate_result:.2f}, 变化: {(delay_rate_origin-delay_rate_result)/delay_rate_origin:.2%} (提升比例)')
        print(f'原始总延误: {origin_total_delay:.2f}, 新总延误: {result_total_delay:.2f}, 变化: {(origin_total_delay-result_total_delay)/origin_total_delay:.2%} (提升比例)')
        print(f'原始平均延误成本: {cost_rate_origin:.2f}, 新平均延误成本: {cost_rate_result:.2f}, 变化: {(cost_rate_origin-cost_rate_result)/cost_rate_origin:.2%} (提升比例)')
        print(f'原始总延误成本: {origin_total_cost:.2f}, 新总延误成本: {result_total_cost:.2f}, 变化: {(origin_total_cost-result_total_cost)/origin_total_cost:.2%} (提升比例)')
        print(f'原始取消数: {origin_cancel_count}, 新取消数: {result_cancel_count}, 航班总数: {result_count}')
        print('----------------------')

# --- Excel process/可视化分析 ---
def process_and_visualize_excel(df, output_excel_path):
    if 'expected_departure' not in df.columns:
        df['expected_departure'] = df['actual_departure']
    df['slot_count']        = df.groupby('new_scheduled_departure')['new_scheduled_departure'].transform('count')
    df['origin_slot_count'] = df.groupby('scheduled_departure')['scheduled_departure'].transform('count')
    def time_str_to_minutes(ts):
        dt = pd.to_datetime(ts)
        return dt.hour * 60 + dt.minute + dt.second / 60.0
    for col in ['scheduled_departure', 'actual_departure', 'expected_departure', 'new_scheduled_departure']:
        df[col + '_min'] = df[col].apply(time_str_to_minutes)
    # delay 系列
    df['new_delay']         = (df['expected_departure_min'] - df['new_scheduled_departure_min'] - 15).clip(lower=0)
    df['origin_delay']      = (df['expected_departure_min'] - df['scheduled_departure_min'] - 15).clip(lower=0)
    df['ture_new_delay']    = (df['actual_departure_min'] - df['new_scheduled_departure_min'] - 15).clip(lower=0)
    df['ture_origin_delay'] = (df['actual_departure_min'] - df['scheduled_departure_min'] - 15).clip(lower=0)
    df['new_delay_cost']         = df.apply(lambda x: delay_cost_caculate(x['new_delay'], x['passenger_count']), axis=1)
    df['origin_delay_cost']      = df.apply(lambda x: delay_cost_caculate(x['origin_delay'], x['passenger_count']), axis=1)
    df['ture_new_cost']          = df.apply(lambda x: delay_cost_caculate(x['ture_new_delay'], x['passenger_count']), axis=1)
    df['ture_origin_cost']       = df.apply(lambda x: delay_cost_caculate(x['ture_origin_delay'], x['passenger_count']), axis=1)

    df['delta_delay']       = df['origin_delay'] - df['new_delay']
    df['ture_delta_delay']  = df['ture_origin_delay'] - df['ture_new_delay']
    df['delta_cost']        = df['origin_delay_cost'] - df['new_delay_cost']
    df['ture_delta_cost']   = df['ture_origin_cost'] - df['ture_new_cost']
    # 保存中间结果
    df.to_excel(output_excel_path, index=False)
    # 可视化
    sns.set_style('whitegrid')
    # slot 对比直方图
    bins = np.arange(0, 24*60+1, 5)
    df['orig_min_day'] = (pd.to_datetime(df['scheduled_departure']) - pd.to_datetime(df['scheduled_departure']).dt.normalize()).dt.total_seconds() / 60
    df['new_min_day']  = (pd.to_datetime(df['new_scheduled_departure']) - pd.to_datetime(df['new_scheduled_departure']).dt.normalize()).dt.total_seconds() / 60
    fig1, ax1 = plt.subplots(figsize=(12, 4))
    ax1.hist(df['orig_min_day'], bins=bins, alpha=0.5, label='origin_slot_count', color='steelblue')
    ax1.hist(df['new_min_day'],  bins=bins, alpha=0.5, label='slot_count', color='orange')
    ax1.set_xlabel('Time of day (minutes from 00:00)')
    ax1.set_ylabel('Number of flights')
    ax1.set_title('Slot count comparison (5-min bins)')
    ax1.legend()
    fig1.tight_layout()
    fig1.savefig('slot_count_comparison.png', dpi=150)

    # delta_delay 对比图
    fig2, ax2 = plt.subplots(figsize=(14, 4))
    x = np.arange(len(df))
    width = 0.4
    ax2.bar(x - width/2, df['delta_delay'],      width, label='delta_delay (expected)', color='seagreen')
    ax2.bar(x + width/2, df['ture_delta_delay'], width, label='ture_delta_delay (actual)', color='crimson')
    ax2.set_xlabel('Flight index')
    ax2.set_ylabel('Delay reduction (minutes)')
    ax2.set_title('Delta delay per flight')
    ax2.legend()
    fig2.tight_layout()
    fig2.savefig('delta_delay_comparison.png', dpi=150)

    # 延误改善率
    total_origin_delay      = df['origin_delay'].sum()
    total_new_delay         = df['new_delay'].sum()
    total_ture_origin_delay = df['ture_origin_delay'].sum()
    total_ture_new_delay    = df['ture_new_delay'].sum()
    total_origin_cost      = df['origin_delay_cost'].sum()
    total_new_cost         = df['new_delay_cost'].sum()
    total_ture_origin_cost = df['ture_origin_cost'].sum()
    total_ture_new_cost    = df['ture_new_cost'].sum()

    improve_rate_expected = ((total_origin_delay - total_new_delay) / total_origin_delay) * 100 if total_origin_delay > 0 else 0
    improve_rate_actual   = ((total_ture_origin_delay - total_ture_new_delay) / total_ture_origin_delay) * 100 if total_ture_origin_delay > 0 else 0
    improve_cost_expected = ((total_origin_cost - total_new_cost) / total_origin_cost) * 100 if total_origin_cost > 0 else 0
    improve_cost_actual   = ((total_ture_origin_cost - total_ture_new_cost) / total_ture_origin_cost) * 100 if total_ture_origin_cost > 0 else 0

    summary_text = (
        f"Expected delay improvement: {total_origin_delay:.1f} → {total_new_delay:.1f} "
        f"({improve_rate_expected:.1f}%)\n"
        f"Actual   delay improvement: {total_ture_origin_delay:.1f} → {total_ture_new_delay:.1f} "
        f"({improve_rate_actual:.1f}%)\n"
        f"Expected delay cost improvement: {total_origin_cost:.1f} → {total_new_cost:.1f} "
        f"({improve_cost_expected:.1f}%)\n"
        f"Actual   delay cost improvement: {total_ture_origin_cost:.1f} → {total_ture_new_cost:.1f} "
        f"({improve_cost_actual:.1f}%)"
    )
    print(summary_text)

    fig3, ax3 = plt.subplots(figsize=(6, 3))
    sns.barplot(x=[improve_rate_expected, improve_rate_actual],
                y=['Expected', 'Actual'],
                palette=['seagreen', 'crimson'], ax=ax3)
    ax3.set_xlabel('Improvement Rate (%)')
    ax3.set_title('Overall Delay Improvement')
    fig3.tight_layout()
    fig3.savefig('improvement_rate.png', dpi=150)

    # 延误成本对比曲线
    fig4, ax4 = plt.subplots(figsize=(14,4))
    ax4.plot(df['new_delay_cost'], label='New Delay Cost (expected)', color='blue')
    ax4.plot(df['ture_new_cost'], label='New Delay Cost (actual)', color='red')
    ax4.set_title('Delay Cost per Flight')
    ax4.set_xlabel('Flight index')
    ax4.set_ylabel('Cost')
    ax4.legend()
    fig4.tight_layout()
    fig4.savefig('delay_cost_per_flight.png', dpi=150)

    # 插入 Excel
    wb = load_workbook(output_excel_path)
    ws = wb.create_sheet('Charts')
    ws['A1'] = summary_text.replace('\n', '  ')

    img1 = XLImage('slot_count_comparison.png');      img1.anchor = 'A3';  ws.add_image(img1)
    img2 = XLImage('delta_delay_comparison.png');     img2.anchor = 'A22'; ws.add_image(img2)
    img3 = XLImage('improvement_rate.png');           img3.anchor = 'A42'; ws.add_image(img3)
    img4 = XLImage('delay_cost_per_flight.png');      img4.anchor = 'A62'; ws.add_image(img4)

    wb.save(output_excel_path)
    print(f'所有图表已生成并插入到 Excel 的 "Charts" 工作表 ({output_excel_path})。')


# ============= 合并航班功能 =============
def merge_flights(assignment: pd.DataFrame) -> pd.DataFrame:
    """
    assignment: 排班结果表，必须包含 flight_id、scheduled_departure、new_scheduled_departure、cancelled、
        passenger_count、passenger_count_max、arrival_place 等字段。
    返回：合并后更新过的 assignment
    """
    df = assignment.copy()
    df['merge_note'] = ""  # 用于记录合并情况
    df['merged_to'] = None # 若被并入其他航班，记录目标航班id

    # 只考虑未取消航班，并安全检查列是否存在
    required_cols = ['cancelled', 'passenger_count_max', 'arrival_place']
    if not all(col in df.columns for col in required_cols):
        print("Warning: Required columns for merging not found in assignment DataFrame.")
        return df 

    valid_flights = df[(~df['cancelled']) & (df['passenger_count_max'].notna()) & (df['arrival_place'] != "")]
    merged_A = set()

    for idx_a, row_a in valid_flights.iterrows():
        # 条件1：A航班未被合并，载客率<0.5，延误>200min
        passenger_count_max_a = row_a.get('passenger_count_max', 0) 
        passenger_count_a = row_a.get('passenger_count', 0) 

        if passenger_count_max_a <= 0 or (passenger_count_a / passenger_count_max_a >= 0.5):
            continue
        # 延误计算：new_delay_minutes 
        if ('new_delay_minutes' not in row_a) or (row_a['new_delay_minutes'] <= 200):
            continue
        if row_a['flight_id'] in merged_A:
            continue

        # 寻找可合并B航班（目的地相同，且合并后总人数<=B的额定载客量，B不能是A本身）
        candidates = valid_flights[
            (valid_flights['arrival_place'] == row_a['arrival_place']) &
            (valid_flights['flight_id'] != row_a['flight_id']) &
            (~valid_flights['flight_id'].isin(merged_A))
        ].copy() # Create a copy to avoid SettingWithCopyWarning

        for idx_b, row_b in candidates.iterrows():
            passenger_count_max_b = row_b.get('passenger_count_max', 0) 
            passenger_count_b = row_b.get('passenger_count', 0)
            total_passengers = passenger_count_a + passenger_count_b

            # B航班额定载客量有效且足够
            if passenger_count_max_b <= 0 or total_passengers > passenger_count_max_b:
                continue
            # 合并！A航班被并入B航班
            # A航班被标记为取消（但无取消损失），延误成本重新计算，merge_note标记
            # B航班不变
            df.loc[idx_a, 'cancelled'] = True
            df.loc[idx_a, 'merge_note'] = f"Merged to {row_b['flight_id']}"
            df.loc[idx_a, 'merged_to'] = row_b['flight_id']
            # 不计取消损失
            scheduled_departure_a = pd.to_datetime(row_a['scheduled_departure'])
            new_scheduled_departure_b = pd.to_datetime(row_b['new_scheduled_departure'])
            if pd.notna(scheduled_departure_a) and pd.notna(new_scheduled_departure_b):
                delay_minutes = (new_scheduled_departure_b - scheduled_departure_a).total_seconds()/60.0 - 15
                df.loc[idx_a, 'new_delay_cost'] = passenger_count_a * max(0, delay_minutes)
                df.loc[idx_a, 'new_delay_minutes'] = max(0, delay_minutes)
            else:
                df.loc[idx_a, 'new_delay_cost'] = 0 # Or other appropriate value
                df.loc[idx_a, 'new_delay_minutes'] = 0 # Or other appropriate value


            # 其他成本字段同步
            for field in ['ture_new_cost', 'ture_new_delay']:
                if (field in df.columns) and ('actual_departure' in row_b):
                    actual_departure_b = pd.to_datetime(row_b['actual_departure'])
                    if pd.notna(scheduled_departure_a) and pd.notna(actual_departure_b):
                        delay_minutes_true = (actual_departure_b - scheduled_departure_a).total_seconds()/60.0 - 15
                        df.loc[idx_a, field] = passenger_count_a * max(0, delay_minutes_true)
                    else:
                        df.loc[idx_a, field] = 0 # Or other appropriate value


            merged_A.add(row_a['flight_id'])
            break # 每个A只合并一次

    return df

def pad_flights_to_batch(flights_df, batch_size, slot_count):
    n = len(flights_df)
    if n >= batch_size:
        df = flights_df.iloc[:batch_size].copy()
        real_mask = np.ones(batch_size, dtype=bool)
        return df, real_mask
    else:
        df_real = flights_df.copy()
        virtual_rows = []
        max_sch_dep = df_real['scheduled_departure'].max() if not df_real.empty else datetime.now()

        for i in range(batch_size - n):
            virtual_row = {
                'flight_id': f'virtual_{i}',
                'scheduled_departure': max_sch_dep + timedelta(minutes=5*(i+1)),
                'actual_departure': max_sch_dep + timedelta(minutes=10*(i+1)),
                'expected_departure': max_sch_dep + timedelta(minutes=10*(i+1)),
                'passenger_count': 0,
                'passenger_count_max': 0, # Add default for virtual flights
                'arrival_place': '' # Add default for virtual flights
            }
            virtual_rows.append(virtual_row)
        df_virtual = pd.DataFrame(virtual_rows)
        df = pd.concat([df_real, df_virtual], ignore_index=True)
        real_mask = np.array([True]*n + [False]*(batch_size-n))
        return df, real_mask

def pad_slots_to_count(slots, slot_count, window_end):
    n = len(slots)
    if n >= slot_count:
        return slots, np.ones(slot_count, dtype=bool)
    else:
        virtual_slots = []
        t = slots[-1]['slot_end'] if n > 0 else window_end
        for i in range(slot_count - n):
            slot_start = t + timedelta(minutes=5*i)
            slot_end = slot_start + timedelta(minutes=5)
            virtual_slots.append({'slot_start': slot_start, 'slot_end': slot_end})
        all_slots = slots + virtual_slots
        real_mask = np.array([True]*n + [False]*(slot_count-n))
        return all_slots, real_mask

def get_action_mask_with_virtuals(env, flight_real_mask, slot_real_mask):
    n_flight, n_slot = len(env.flights_df), len(env.slots)
    mask = env.get_action_mask()
    for i, is_real_flight in enumerate(flight_real_mask):
        if not is_real_flight:
            mask[i, :] = False
    for j, is_real_slot in enumerate(slot_real_mask):
        if not is_real_slot:
            mask[:, j] = False
    return mask

def batch_predict(
    agent, model_path,
    flights_df, slots, available_slots, slot_occupied_counts,
    slot_length, max_per_slot,
    batch_flight_size=46, slot_count=72, device='cpu',
    slot_real_mask=None,
    global_slot_key2idx=None, global_slot_counts=None,
    allow_cancel=True,
    use_expected_depature=False
):
    total_results = []
    n_flight = len(flights_df)
    flight_ptr = 0
    batch_id = 0
    unassigned_flights = []
    all_flight_ids = set(flights_df['flight_id'])
    assigned_flight_ids = set()

    while flight_ptr < n_flight:
        batch_df = flights_df.iloc[flight_ptr:flight_ptr+batch_flight_size]
        batch_df, flight_real_mask = pad_flights_to_batch(batch_df, batch_flight_size, slot_count)
        env = FlightRecoveryEnv(
            batch_df, slots, available_slots, slot_length=slot_length, max_per_slot=max_per_slot,
            global_slot_key2idx=global_slot_key2idx,
            global_slot_counts=global_slot_counts,
            allow_cancel=allow_cancel,
            use_expected_depature=use_expected_depature
        )

        state = env.reset()
        done = False
        n_slot = len(slots)
        while not done:
            mask = get_action_mask_with_virtuals(env, flight_real_mask, slot_real_mask)
            mask_flat = mask.flatten()
            if not mask_flat.any():
                break
            with torch.no_grad():
                action_idx, log_prob, value, _ = agent.get_action(state, mask, device=device, temperature=1.0)
            flight_idx, slot_idx = decode_action(action_idx, batch_flight_size, n_slot)

            if not mask_flat[action_idx]:
                possible_idxs = np.where(mask_flat)[0]
                if len(possible_idxs) > 0:
                    action_idx = np.random.choice(possible_idxs)
                    flight_idx, slot_idx = decode_action(action_idx, batch_flight_size, n_slot)
                else:
                    break

            next_state, reward, done = env.step((flight_idx, slot_idx))
            state = next_state

        assign_df = env.get_assignment_results()
        assign_df['batch_id'] = batch_id
        assign_df = assign_df.iloc[:sum(flight_real_mask)]
        assigned_flight_ids.update(assign_df['flight_id'].tolist())
        total_results.append(assign_df)
        flight_ptr += batch_flight_size
        batch_id += 1

    final_assign_df = pd.concat(total_results, ignore_index=True)
    missing_flights = set(flights_df['flight_id']) - set(final_assign_df['flight_id'])
    if missing_flights:
        missing_rows = []
        for fid in missing_flights:
            flight_row = flights_df[flights_df['flight_id'] == fid].iloc[0]
            missing_rows.append({
                'flight_id': fid,
                'scheduled_departure': flight_row['scheduled_departure'],
                'actual_departure': flight_row['actual_departure'] if 'actual_departure' in flight_row else pd.NaT, # Handle missing actual_departure
                'expected_departure': flight_row.get('expected_departure', flight_row.get('actual_departure', pd.NaT)), # Handle missing expected/actual
                'passenger_count': flight_row['passenger_count'],
                'new_scheduled_departure': None,
                'new_delay_minutes': 8888,
                'new_delay_cost': 8888*flight_row.get('passenger_count', 0), # Safely get passenger_count
                'cancelled': True,
                'origin_delay_minutes': max(0, (flight_row.get('expected_departure', flight_row.get('actual_departure', pd.NaT)) - flight_row['scheduled_departure']).total_seconds() / 60.0 - 15) if pd.notna(flight_row.get('expected_departure', flight_row.get('actual_departure', pd.NaT))) else 0, # Safely calculate delay
                'origin_delay_cost': max(0, (flight_row.get('expected_departure', flight_row.get('actual_departure', pd.NaT)) - flight_row['scheduled_departure']).total_seconds() / 60.0 - 15) * flight_row.get('passenger_count', 0) if pd.notna(flight_row.get('expected_departure', flight_row.get('actual_departure', pd.NaT))) else 0, # Safely calculate cost
                'ture_new_delay': 8888,
                'ture_new_cost': 8888*flight_row.get('passenger_count', 0), # Safely get passenger_count
                'ture_origin_delay': max(0, (flight_row['actual_departure'] - flight_row['scheduled_departure']).total_seconds() / 60.0 - 15) if pd.notna(flight_row.get('actual_departure', pd.NaT)) else 0, # Safely calculate delay
                'ture_origin_cost': max(0, (flight_row['actual_departure'] - flight_row['scheduled_departure']).total_seconds() / 60.0 - 15) * flight_row.get('passenger_count', 0) if pd.notna(flight_row.get('actual_departure', pd.NaT)) else 0, # Safely calculate cost
                'batch_id': -1
            })
        final_assign_df = pd.concat([final_assign_df, pd.DataFrame(missing_rows)], ignore_index=True)

    return final_assign_df, pd.DataFrame()

def get_time_windows_for_whole_day(start_time, slot_count=72, slot_length=5):
    windows = []
    cur = start_time
    day_end = (start_time.replace(hour=0, minute=0, second=0, microsecond=0) + timedelta(days=1))
    while cur < day_end:
        window_start = cur
        window_end = window_start + timedelta(minutes=slot_length*slot_count)
        if window_end > day_end:
            window_end = day_end
        windows.append((window_start, window_end))
        cur = window_start + timedelta(minutes=slot_length*int(slot_count/2))  # 滑窗
    return windows

def whole_day_predict(
    model_path, excel_path,
    slot_length=5, max_per_slot=5, batch_flight_size=46, slot_count=72,
    allow_cancel=False
):
    loader = FlightDataLoader(excel_path, mode='test')
    df = loader.get_data()
    df['date_only'] = df['scheduled_departure'].dt.strftime('%Y-%m-%d')
    date0 = df['date_only'].min()
    day_start = datetime.strptime(date0 + ' 00:00:00', '%Y-%m-%d %H:%M:%S')
    windows = get_time_windows_for_whole_day(day_start, slot_count=slot_count, slot_length=slot_length)
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    state_dim = batch_flight_size + slot_count
    action_dim = batch_flight_size * (slot_count + 1)
    agent = MaskedActorCritic(state_dim, action_dim).to(device)
    agent.load_state_dict(torch.load(model_path))
    agent.eval()

    slots_per_window = []
    for window_start, window_end in windows:
        slots = generate_time_slots(window_start, window_end, slot_length)
        slots_per_window.append(slots)
    global_slot_key2idx, idx2slot = build_global_slot_index(slots_per_window)
    global_slot_counts = [0] * len(idx2slot)

    assigned_flight_ids = set()
    all_results = []

    for i, (window_start, window_end) in enumerate(windows):
        flights = df[
            (df['scheduled_departure'] >= window_start) &
            (df['scheduled_departure'] < window_end) &
            (~df['flight_id'].isin(assigned_flight_ids))
        ].copy()
        slots = slots_per_window[i]
        slots, slot_real_mask = pad_slots_to_count(slots, slot_count, window_end)
        available_slots = filter_available_slots(df, window_start, window_end, slot_length)
        available_slots = list(available_slots) + [False]*(slot_count-len(available_slots))
        slot_occupied_counts = [0 for _ in range(slot_count)]
        if len(flights) == 0:
            continue
        res_df, _ = batch_predict(
            agent, model_path, flights, slots, available_slots, slot_occupied_counts,
            slot_length, max_per_slot,
            batch_flight_size=batch_flight_size, slot_count=slot_count, device=device,
            slot_real_mask=slot_real_mask,
            global_slot_key2idx=global_slot_key2idx,
            global_slot_counts=global_slot_counts,
            allow_cancel=allow_cancel,
            use_expected_depature=True
        )
        res_df['window_start'] = window_start
        res_df['window_end'] = window_end
        all_results.append(res_df)
        assigned_flight_ids.update(res_df['flight_id'].unique())

    if len(all_results) == 0:
        print("全天没有任何航班。")
        return df, pd.DataFrame()
    final_assignment = pd.concat(all_results, ignore_index=True)
    final_assignment = final_assignment.reset_index(drop=True)
    final_assignment = final_assignment.drop_duplicates(subset=['flight_id'], keep='last').reset_index(drop=True)

    # ======= 合并航班功能 =======
    merged_assignment = merge_flights(final_assignment)
    return df, merged_assignment