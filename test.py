from ulti_0802 import *

def main():
    excel_path = '离港延误预测结果05-23（MAE=40.7577, RMSE=72.3417）.xlsx'
    model_path = 'ppo_model.pt'
    df, assignment = whole_day_predict(
        model_path, excel_path,
        slot_length=5, max_per_slot=5, batch_flight_size=46, slot_count=72
    )
    output_excel = '离港延误预测结果05-23_processed.xlsx'
    if assignment is not None and not assignment.empty:
        process_and_visualize_excel(assignment, output_excel)
    else:
        print("无排班结果，无需处理。")

if __name__ == '__main__':
    main()