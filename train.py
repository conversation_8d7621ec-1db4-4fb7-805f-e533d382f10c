from ulti import *

from datetime import datetime

def main(): # 训练
    excel_path = 'data_withp.xlsx'
    loader = FlightDataLoader(excel_path, mode='train')
    df = loader.get_data()

    window1_start = datetime.strptime('2025-05-23 13:00:00', '%Y-%m-%d %H:%M:%S')
    window1_end = datetime.strptime('2025-05-23 16:00:00', '%Y-%m-%d %H:%M:%S')
    window2_start = window1_start
    window2_end = datetime.strptime('2025-05-23 19:00:00', '%Y-%m-%d %H:%M:%S')

    flights_to_recover = df[(df['scheduled_departure'] >= window1_start) & (df['scheduled_departure'] < window1_end)].copy()

    slots = generate_time_slots(window2_start, window2_end, slot_length=5)
    available_slots = filter_available_slots(df, window2_start, window2_end, slot_length=5)

    env = FlightRecoveryEnv(
        flights_to_recover, slots, available_slots,
        slot_length=5, max_per_slot=5,
        cancel_penalty=-2000,
        slot_reward_pos=10.0, slot_reward_neg=-3
    )

    agent, rewards_history, total_delay_history, total_cost_history, assignment_last, assignment_best = train_masked_ppo(
        env, num_episodes=5000, model_save_path='0724/ppo_model.pt',
        batch_size=128, ppo_epochs=4, lr=1e-4, entropy_coef=0.05
    )

    visualize_assignment(assignment_last, save_path='0724/assignment_result_last.xlsx', tag='LAST')
    visualize_assignment(assignment_best, save_path='0724/assignment_result_best.xlsx', tag='BEST')

    statistics_compare(df, assignment_last, assignment_best)

if __name__ == '__main__':
    main()